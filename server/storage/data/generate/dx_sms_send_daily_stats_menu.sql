-- hotgo自动生成菜单权限SQL 通常情况下只在首次生成代码时自动执行一次
-- 如需再次执行请先手动删除生成的菜单权限和SQL文件：D:\go-project\src\dongyun.org\dy-dxpt\admin\server\storage\data\generate\dx_sms_send_daily_stats_menu.sql
-- Version: 2.15.11
-- Date: 2025-01-03 23:12:19
-- Link https://github.com/bufanyun/hotgo

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;

--
-- 数据库： `dxpt`
--

-- --------------------------------------------------------

--
-- 插入表中的数据 `hg_admin_menu`
--


SET @now := now();


-- 菜单目录
INSERT INTO `hg_admin_menu` (`id`, `pid`, `title`, `name`, `path`, `icon`, `type`, `redirect`, `permissions`, `permission_name`, `component`, `always_show`, `active_menu`, `is_root`, `is_frame`, `frame_src`, `keep_alive`, `hidden`, `affix`, `level`, `tree`, `sort`, `remark`, `status`, `created_at`, `updated_at`) VALUES (NULL, '2503', '短信报表', 'dxSmsSendDailyStats', '/dxSmsSendDailyStats', '', '1', '/reportStatistics/dxSmsSendDailyStats/index', '', '', 'ParentLayout', '1', '', '0', '0', '', '0', '0', '0', '2', 'tr_2503 ', '0', '', '1', @now, @now);


SET @dirId = LAST_INSERT_ID();


-- 菜单页面
-- 列表
INSERT INTO `hg_admin_menu` (`id`, `pid`, `title`, `name`, `path`, `icon`, `type`, `redirect`, `permissions`, `permission_name`, `component`, `always_show`, `active_menu`, `is_root`, `is_frame`, `frame_src`, `keep_alive`, `hidden`, `affix`, `level`, `tree`, `sort`, `remark`, `status`, `created_at`, `updated_at`) VALUES (NULL, @dirId, '短信报表列表', 'dxSmsSendDailyStatsIndex', 'index', '', '2', '', '/dxSmsSendDailyStats/list', '', '/dxSmsSendDailyStats/index', '1', 'dxSmsSendDailyStats', '0', '0', '', '0', '1', '0', '3', CONCAT('tr_2503 tr_', @dirId,' '), '10', '', '1', @now, @now);


SET @listId = LAST_INSERT_ID();


-- 菜单按钮


SET @editId = LAST_INSERT_ID();





-- 导出
INSERT INTO `hg_admin_menu` (`id`, `pid`, `title`, `name`, `path`, `icon`, `type`, `redirect`, `permissions`, `permission_name`, `component`, `always_show`, `active_menu`, `is_root`, `is_frame`, `frame_src`, `keep_alive`, `hidden`, `affix`, `level`, `tree`, `sort`, `remark`, `status`, `created_at`, `updated_at`) VALUES (NULL, @listId, '导出短信报表', 'dxSmsSendDailyStatsExport', '', '', '3', '', '/dxSmsSendDailyStats/export', '', '', '1', '', '0', '0', '', '0', '0', '0', '4', CONCAT('tr_2503 tr_', @dirId, ' tr_', @listId,' '), '70', '', '1', @now, @now);


COMMIT;