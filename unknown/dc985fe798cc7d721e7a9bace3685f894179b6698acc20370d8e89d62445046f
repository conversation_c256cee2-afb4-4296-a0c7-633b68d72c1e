// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysNotifyConfigDao is the data access object for table hg_sys_notify_config.
type SysNotifyConfigDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of current DAO.
	columns SysNotifyConfigColumns // columns contains all the column names of Table for convenient usage.
}

// SysNotifyConfigColumns defines and stores column names for table hg_sys_notify_config.
type SysNotifyConfigColumns struct {
	Id        string // 主键ID
	Category  string // 通知分类
	Name      string // 通知名称
	SignText  string // 短信签名
	Content   string // 短信内容
	Mobiles   string // 通知号码
	MemberId  string // 用户ID
	Status    string // 状态
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// sysNotifyConfigColumns holds the columns for table hg_sys_notify_config.
var sysNotifyConfigColumns = SysNotifyConfigColumns{
	Id:        "id",
	Category:  "category",
	Name:      "name",
	SignText:  "sign_text",
	Content:   "content",
	Mobiles:   "mobiles",
	MemberId:  "member_id",
	Status:    "status",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewSysNotifyConfigDao creates and returns a new DAO object for table data access.
func NewSysNotifyConfigDao() *SysNotifyConfigDao {
	return &SysNotifyConfigDao{
		group:   "default",
		table:   "hg_sys_notify_config",
		columns: sysNotifyConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SysNotifyConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SysNotifyConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SysNotifyConfigDao) Columns() SysNotifyConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SysNotifyConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SysNotifyConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SysNotifyConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
