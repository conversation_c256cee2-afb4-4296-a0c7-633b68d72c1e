<template>
  <div>
    <n-drawer v-model:show="showModal" :width="dialogWidth">
      <n-drawer-content title="短信群发详情" closable>
        <n-spin :show="loading" description="请稍候...">
          <n-descriptions label-placement="left" class="py-2" :column="1">
            <n-descriptions-item>
              <template #label>
                用户名称
              </template>
              {{ formValue.memberBySumma?.realName }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                批次号
              </template>
              {{ formValue.batchNo }}
            </n-descriptions-item>
            <n-descriptions-item label="短信模式">
              <n-tag :type="dict.getType('sms_mode', formValue.smsMode)" size="small" class="min-left-space">
                {{ dict.getLabel('sms_mode', formValue.smsMode) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="发送模式">
              <n-tag :type="dict.getType('send_mode', formValue.sendMode)" size="small" class="min-left-space">
                {{ dict.getLabel('send_mode', formValue.sendMode) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item v-if="userStore.isCompanyDept">
              <template #label>
                短信通道
              </template>
              {{ formValue.sysSmsChannelChannelName }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                短信签名
              </template>
              {{ formValue.dxSmsSignSignText }}
            </n-descriptions-item>
            <n-descriptions-item v-if="formValue.smsMode === 2">
              <template #label>
                模板名称
              </template>
              {{ formValue.dxSmsGroupTemplateTplName }}
            </n-descriptions-item>
            <!-- <n-descriptions-item>
              <template #label>
                号码数量
              </template>
              {{ formValue.mobileNum }}
            </n-descriptions-item> -->
            <n-descriptions-item label="发送状态">
              <n-tag :type="dict.getType('send_status', formValue.sendStatus)" size="small" class="min-left-space">
                {{ dict.getLabel('send_status', formValue.sendStatus) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="审核状态">
              <n-tag :type="dict.getType('audit_status', formValue.auditStatus)" size="small" class="min-left-space">
                {{ dict.getLabel('audit_status', formValue.auditStatus) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item v-if="formValue.sendMode === 2">
              <template #label>
                定时发送时间
              </template>
              {{ formValue.scheduleTime }}
            </n-descriptions-item>
            <!-- <n-descriptions-item>
              <template #label>
                周期发送开始日期
              </template>
              {{ formValue.cycleStartDate }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                周期发送结束日期
              </template>
              {{ formValue.cycleEndDate }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                周期发送开始时间
              </template>
              {{ formValue.cycleStartTime }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                周期发送结束时间
              </template>
              {{ formValue.cycleEndTime }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                周期发送频次/天
              </template>
              {{ formValue.cycleFreq }}
            </n-descriptions-item> -->
            <n-descriptions-item>
              <template #label>
                短信内容
              </template>
              <span v-html="formValue.content"></span>
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                备注
              </template>
              <span v-html="formValue.remark"></span>
            </n-descriptions-item>
            <n-descriptions-item v-if="contactGroups.length > 0">
              <template #label>
                通讯录分组
              </template>
              <n-space>
                <n-tag v-for="group in contactGroups" :key="group.key" type="info" size="small">
                  {{ group.name }}
                </n-tag>
              </n-space>
            </n-descriptions-item>
            <n-descriptions-item v-if="formValue.mobiles">
              <template #label>
                发送号码
              </template>
              <span v-html="formValue.mobiles"></span>
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                号码文件
              </template>
              <a v-if="formValue.fileUrl" @click.prevent="download(formValue.fileUrl)">下载文件</a>
            </n-descriptions-item>
          </n-descriptions>
        </n-spin>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { View } from '@/api/dxSmsGroupSend';
  import { State, newState } from './model';
  import { adaModalWidth } from '@/utils/hotgo';
  import { useDictStore } from '@/store/modules/dict';
  import { useUserStore } from '@/store/modules/user';

  const dict = useDictStore();
  const userStore = useUserStore();
  const loading = ref(false);
  const showModal = ref(false);
  const formValue = ref(newState(null));
  const contactGroups = ref([]);
  const dialogWidth = computed(() => {
    return adaModalWidth(580);
  });

  // 下载
  function download(url: string) {
    window.open(url);
  }

  // 打开模态框
  function openModal(state: State) {
    showModal.value = true;
    loading.value = true;
    View({ id: state.id })
      .then((res) => {
        formValue.value = res;
        // 解析通讯录分组数据
        parseContactGroups(res.contactGroups);
      })
      .finally(() => {
        loading.value = false;
      });
  }

  // 解析通讯录分组数据
  function parseContactGroups(contactGroupsData: any) {
    contactGroups.value = [];
    if (contactGroupsData) {
      try {
        // 如果是字符串，尝试解析JSON
        if (typeof contactGroupsData === 'string') {
          contactGroups.value = JSON.parse(contactGroupsData);
        } else if (Array.isArray(contactGroupsData)) {
          // 如果已经是数组，直接使用
          contactGroups.value = contactGroupsData;
        } else if (typeof contactGroupsData === 'object') {
          // 如果是对象，可能需要进一步处理
          contactGroups.value = [contactGroupsData];
        }
      } catch (error) {
        console.error('解析通讯录分组数据失败:', error);
        contactGroups.value = [];
      }
    }
  }

  defineExpose({
    openModal,
  });
</script>

<style lang="less" scoped></style>
