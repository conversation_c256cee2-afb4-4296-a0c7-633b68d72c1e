import { h, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { FormSchema } from '@/components/Form';
import { defRangeShortcuts } from '@/utils/dateUtil';
import { MemberSumma, renderOptionTag, renderPopoverMemberSumma } from '@/utils';
import { useDictStore } from '@/store/modules/dict';
import { useUserStore } from '@/store/modules/user';

const dict = useDictStore();
const userStore = useUserStore();

export class State {
  public id = 0; // ID
  public memberId = 0; // 用户ID
  public signText = ''; // 签名文本
  public remark = ''; // 备注
  public status = 1; // 状态
  public auditStatus = null; // 审核状态
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间
  public deletedAt = ''; // 删除时间
  public memberBySumma?: null | MemberSumma = null; // 创建人摘要信息

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则
export const rules = {
  signText: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'string',
    message: '请输入签名文本',
  },
};

// 表格搜索表单
export const schemas = ref<FormSchema[]>([
  {
    field: 'adminMemberRealName',
    component: 'NInput',
    label: '企业名称',
    componentProps: {
      placeholder: '请输入姓名',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    ifShow: () => {
      return userStore.isCompanyDept
    },
  },
  {
    field: 'adminMemberUserName',
    component: 'NInput',
    label: '子账号',
    componentProps: {
      placeholder: '请输入用户名',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    ifShow: () => {
      return !userStore.isUserDept
    },
  },
  {
    field: 'signText',
    component: 'NInput',
    label: '签名',
    componentProps: {
      placeholder: '请输入签名',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'status',
    component: 'NSelect',
    label: '状态',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择状态',
      options: dict.getOption('sys_normal_disable'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'auditStatus',
    component: 'NSelect',
    label: '审核状态',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择审核状态',
      options: dict.getOption('audit_status'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'createdAt',
    component: 'NDatePicker',
    label: '创建时间',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      shortcuts: defRangeShortcuts(),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
]);

// 表格列
export const columns = [
  // {
  //   title: 'ID',
  //   key: 'id',
  //   align: 'left',
  //   width: -1,
  // },
  {
    title: '企业名称',
    key: 'memberBySumma',
    align: 'left',
    width: -1,
    render(row: State) {
      // return renderPopoverMemberSumma(row.memberBySumma);
      return row.memberBySumma?.realName;
    },
    ifShow: () => {
      return userStore.isCompanyDept
    },
  },
  {
    title: '子账号',
    key: 'memberBySumma',
    align: 'left',
    width: -1,
    render(row: State) {
      // return renderPopoverMemberSumma(row.memberBySumma);
      return row.memberBySumma?.username;
    },
    ifShow: () => {
      return !userStore.isUserDept
    },
  },
  {
    title: '签名',
    key: 'signText',
    align: 'left',
    width: -1,
  },
  {
    title: '备注',
    key: 'remark',
    align: 'left',
    width: -1,
  },
  {
    title: '创建时间',
    key: 'createdAt',
    align: 'left',
    width: -1,
  },
  {
    title: '状态',
    key: 'status',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('sys_normal_disable', row.status);
    },
  },
  {
    title: '审核状态',
    key: 'auditStatus',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('audit_status', row.auditStatus);
    },
  },
];

// 加载字典数据选项
export function loadOptions() {
  dict.loadOptions(['sys_normal_disable', 'audit_status']);
}