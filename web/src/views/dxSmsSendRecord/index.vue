<template>
  <div>
    <div class="n-layout-page-header">
      <n-card :bordered="false" title="短信记录">
        <!--  这是由系统生成的CURD表格，你可以将此行注释改为表格的描述 -->
      </n-card>
    </div>
    <n-card :bordered="false" class="proCard">
      <BasicForm  ref="searchFormRef" @register="register" @submit="reloadTable" @reset="reloadTable" @keyup.enter="reloadTable">
        <template #statusSlot="{ model, field }">
          <n-input v-model:value="model[field]" />
        </template>
      </BasicForm>
      <BasicTable  ref="actionRef" openChecked :columns="columns" :request="loadDataTable" :row-key="(row) => row.id" :actionColumn="actionColumn" :scroll-x="scrollX" :resizeHeightOffset="-10000"  :checked-row-keys="checkedIds" @update:checked-row-keys="handleOnCheckedRow">
        <template #tableTitle>
          <div class="table-header">
            <div class="left-buttons">
              <n-button type="primary" @click="handleExport" class="min-left-space" v-if="hasPermission(['/dxSmsSendRecord/export'])">
                <template #icon>
                  <n-icon>
                    <ExportOutlined />
                  </n-icon>
                </template>
                导出
              </n-button>
              <n-button type="primary" @click="handleResend" class="min-left-space" v-if="hasPermission(['/dxSmsSendRecord/resendFailed'])">
                <template #icon>
                  <n-icon>
                    <ReloadOutlined />
                  </n-icon>
                </template>
                失败重发
              </n-button>
              <div class="statistics-summary" v-if="hasPermission(['/dxSmsSendRecord/statTotalBilling'])">
                <span>总计费条数：{{ statistics.totalBilling.toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </template>
      </BasicTable>
    </n-card>
    <View ref="viewRef" />
    
    <!-- 添加重发模态窗 -->
    <n-modal v-model:show="showResendModal" preset="dialog" title="失败短信重发">
      <n-form
        ref="resendFormRef"
        :model="resendForm"
        :rules="resendRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="批次号" path="batchNo">
          <n-input v-model:value="resendForm.batchNo" placeholder="请输入批次号" />
        </n-form-item>
        <n-form-item label="短信通道" path="channelId" v-if="userStore.isCompanyDept">
          <n-tree-select
            placeholder="请选择短信通道"
            v-model:value="resendForm.channelId"
            :options="channelTreeOption"
            key-field="id"
            label-field="channelName"
            clearable
            filterable
            default-expand-all
            show-path
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showResendModal = false">取消</n-button>
          <n-button type="primary" @click="handleResendSubmit" :loading="resendLoading">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { h, reactive, ref, computed, onMounted, onUnmounted } from 'vue';
  import { useDialog, useMessage } from 'naive-ui';
  import { useRoute } from 'vue-router';
  import { BasicTable, TableAction } from '@/components/Table';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useDictStore } from '@/store/modules/dict';
  import { List, Export, ResendFailed, StatTotalBilling } from '@/api/dxSmsSendRecord';
  import { TreeOption } from '@/api/smsChannel';
  import { ExportOutlined, ReloadOutlined } from '@vicons/antd';
  import { columns as baseColumns, schemas, loadOptions, State, getDefaultTimeRange } from './model';
  import { adaTableScrollX } from '@/utils/hotgo';
  import View from './view.vue';
  import { createIndexColumn } from '@/utils/table';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();
  const dict = useDictStore();
  const dialog = useDialog();
  const message = useMessage();
  const route = useRoute();
  const { hasPermission } = usePermission();
  const actionRef = ref();
  const searchFormRef = ref<any>({});
  const editRef = ref();
  const viewRef = ref();
  const checkedIds = ref([]);
  const tableLoaded = ref(false);
  const isFirstLoad = ref(true); // 标记是否为首次加载

  const actionColumn = (userStore.isTenantDept || userStore.isUserDept)? ref():reactive({
    width: 100,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record: State) {
      return h(TableAction as any, {
        style: 'button',
        actions: [
          {
            label: '详情',
            onclick: handleView.bind(null, record),
            auth: ['/dxSmsSendRecord/view'],
          }
        ],
      });
    },
  } as const);

  const baseIndexColumn = createIndexColumn(80);

  const columns = computed(() => {
    if (!tableLoaded.value) {
      return baseColumns;
    }
    return [baseIndexColumn, ...baseColumns];
  });

  const scrollX = computed(() => {
    const width = actionColumn ? (typeof actionColumn === 'object' ? actionColumn.width : 0) : 0;
    return adaTableScrollX(columns.value, width);
  });

  const [register, {}] = useForm({
    gridProps: { cols: '1 s:1 m:2 l:3 xl:4 2xl:4' },
    labelWidth: 80,
    schemas,
    inline: false,  // 设置为非内联模式
    showAdvancedButton: false,  // 不显示展开/收起按钮，表单将始终展开
  });

  // 加载表格数据
  const loadDataTable = async (res) => {
    baseIndexColumn.updatePagination(res.page, res.pageSize);
    
    // 检查URL参数，如果有相关参数且搜索表单还没有设置，则直接使用URL参数
    const adminMemberRealName = route.query.adminMemberRealName as string;
    const adminMemberUserName = route.query.adminMemberUserName as string;
    const batchNo = route.query.batchNo as string;
    let searchParams = { ...searchFormRef.value?.formModel, ...res };
    
    // 只在首次加载时从URL参数中获取值
    if (isFirstLoad.value) {
      // 处理企业名称参数
      if (adminMemberRealName && (!searchFormRef.value?.formModel?.adminMemberRealName)) {
        searchParams.adminMemberRealName = adminMemberRealName;
        // 同时更新搜索表单显示
        if (searchFormRef.value?.formModel) {
          searchFormRef.value.formModel.adminMemberRealName = adminMemberRealName;
        }
      }
      
      // 处理子账号参数
      if (adminMemberUserName && (!searchFormRef.value?.formModel?.adminMemberUserName)) {
        searchParams.adminMemberUserName = adminMemberUserName;
        // 同时更新搜索表单显示
        if (searchFormRef.value?.formModel) {
          searchFormRef.value.formModel.adminMemberUserName = adminMemberUserName;
        }
      }
      
      // 处理批次号参数
      if (batchNo && (!searchFormRef.value?.formModel?.batchNo)) {
        searchParams.batchNo = batchNo;
        // 同时更新搜索表单显示
        if (searchFormRef.value?.formModel) {
          searchFormRef.value.formModel.batchNo = batchNo;
        }
      }
    }
    
    const data = await List(searchParams);
    if (!tableLoaded.value) {
      tableLoaded.value = true;
    }
    return data;
  };

  // 更新选中的行
  function handleOnCheckedRow(rowKeys) {
    checkedIds.value = rowKeys;
  }

  // 重新加载表格数据
  function reloadTable() {
    isFirstLoad.value = false; // 标记为非首次加载
    actionRef.value?.reload();
    loadStatistics(); // 同时刷新统计数据
  }

  // 查看详情
  function handleView(record: Recordable) {
    viewRef.value.openModal(record);
  }

  // 导出
  function handleExport() {
    message.loading('正在导出列表...', { duration: 1200 });
    Export(searchFormRef.value?.formModel);
  }

  // 短信通道选项
  interface ChannelOption {
    id: number;
    channelName: string;
    children?: ChannelOption[];
  }
  const channelTreeOption = ref<ChannelOption[]>([]);

  // 获取短信通道选项
  async function loadChannelTreeOption() {
    try {
      const options = await TreeOption();
      // 添加原通道选项
      channelTreeOption.value = [
        {
          id: 0,
          channelName: '原通道'
        },
        ...(options as ChannelOption[])
      ];
    } catch (err) {
      console.error('获取短信通道选项失败:', err);
    }
  }

  // 重发相关的状态
  const showResendModal = ref(false);
  const resendLoading = ref(false);
  const resendForm = reactive({
    batchNo: '',
    channelId: 0,  // 默认为0，表示使用原通道
  });
  const resendRules = {
    batchNo: {
      required: true,
      message: '请输入批次号',
      trigger: ['blur', 'input'],
    },
  };

  // 打开重发模态窗
  async function handleResend() {
    resendForm.batchNo = searchFormRef.value?.formModel?.batchNo || '';
    resendForm.channelId = 0;  // 重置为0，表示使用原通道
    if (userStore.isCompanyDept) {
      await loadChannelTreeOption();
    }
    showResendModal.value = true;
  }

  // 提交重发请求
  async function handleResendSubmit() {
    resendLoading.value = true;
    try {
      const { count, batchNo } = await ResendFailed({ 
        batchNo: resendForm.batchNo,
        channelId: resendForm.channelId
      });
      message.success(`重发成功！重发数量：${count}，新批次号：${batchNo}`);
      showResendModal.value = false;
      reloadTable();
    } catch (err: any) {
      //message.error('重发失败：' + (err.message || '未知错误'));
      console.log('重发失败：' + (err.message || '未知错误'));
    } finally {
      resendLoading.value = false;
    }
  }

  // 添加统计数据
  const statistics = reactive({
    totalBilling: 0,
  });

  // 获取统计数据
  const loadStatistics = async () => {
    if (!hasPermission(['/dxSmsSendRecord/statTotalBilling'])) {
      return;
    }
    try {
      const params = searchFormRef.value?.formModel || {};
      const res = await StatTotalBilling(params);
      if (res) {
        statistics.totalBilling = res.totalBillingCount || 0;
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  onMounted(() => {
    loadOptions();
    // 设置默认的时间范围
    if (searchFormRef.value?.formModel) {
      searchFormRef.value.formModel.createdAt = getDefaultTimeRange();
    }
    loadStatistics(); // 初始加载统计数据

    // 添加短信内容显示事件监听
    window.addEventListener('showSmsContent', handleShowSmsContent);
  });

  // 组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('showSmsContent', handleShowSmsContent);
  });

  // 处理显示短信内容的函数
  const handleShowSmsContent = ((event: CustomEvent) => {
    dialog.info({
      title: '短信内容',
      content: event.detail,
      positiveText: '确定'
    });
  }) as EventListener;
</script>

<style lang="less" scoped>
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .left-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

.statistics-summary {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 6px 12px;
  background: #f8f9fe;
  border-radius: 4px;
  
  span {
    font-size: 13px;
    color: #515a6e;
    white-space: nowrap;
  }
}
</style>