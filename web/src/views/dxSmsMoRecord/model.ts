import { h, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { FormSchema } from '@/components/Form';
import { defRangeShortcuts } from '@/utils/dateUtil';
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();

export class State {
  public id = 0; // ID
  public memberId = 0; // 用户ID
  public ct = ''; // 接收时间
  public mobile = ''; // 接收号码
  public sp = ''; // 上行号码所属运营商
  public lc = ''; // 上行号码所属省市
  public ext = ''; // 接入码
  public msg = ''; // 接收内容
  public createdAt = ''; // 接收时间
  public channelId = 0; // 通道ID
  public uid = 0; // 用户账号所属编号，唯一
  public uname = ''; // 用户账号
  public isFetch = 0; // 是否已通过api接口获取
  public fetchTime = ''; // 获取时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const schemas = ref<FormSchema[]>([
  {
    field: 'adminMemberRealName',
    component: 'NInput',
    label: '企业名称',
    componentProps: {
      placeholder: '请输入企业名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    ifShow: () => {
      return userStore.isCompanyDept
    },
  },
  {
    field: 'adminMemberUsername',
    component: 'NInput',
    label: '子账号',
    componentProps: {
      placeholder: '请输入子账号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    ifShow: () => {
      return !userStore.isUserDept
    },
  },
  {
    field: 'ext',
    component: 'NInput',
    label: '发送号码',
    componentProps: {
      placeholder: '请输入发送号码',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'mobile',
    component: 'NInput',
    label: '接收号码',
    componentProps: {
      placeholder: '请输入接收号码',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'createdAt',
    component: 'NDatePicker',
    label: '接收时间',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      shortcuts: defRangeShortcuts(),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  
]);

// 表格列
export const columns = [
  {
    title: '企业名称',
    key: 'adminMemberRealName',
    align: 'left',
    width: -1,
    ifShow: () => {
      return userStore.isCompanyDept
    },
  },
  {
    title: '子账号',
    key: 'adminMemberUsername',
    align: 'left',
    width: -1,
    ifShow: () => {
      return !userStore.isUserDept
    },
  },
  {
    title: '发送号码',
    key: 'ext',
    align: 'left',
    width: -1,
  },
  {
    title: '接收号码',
    key: 'mobile',
    align: 'left',
    width: -1,
  },
  {
    title: '接收内容',
    key: 'msg',
    align: 'left',
    width: -1,
  },
  {
    title: '接收时间',
    key: 'createdAt',
    align: 'left',
    width: -1,
  },
];