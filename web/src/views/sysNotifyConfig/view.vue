<template>
  <div>
    <n-drawer v-model:show="showModal" :width="dialogWidth">
      <n-drawer-content title="系统_审核通知配置详情" closable>
        <n-spin :show="loading" description="请稍候...">
          <n-descriptions label-placement="left" class="py-2" :column="1">
            <n-descriptions-item label="通知分类">
              <n-tag :type="dict.getType('audit_notify_config', formValue.category)" size="small" class="min-left-space">
                {{ dict.getLabel('audit_notify_config', formValue.category) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                通知名称
              </template>
              {{ formValue.name }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                短信签名
              </template>
              {{ formValue.signText }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                短信内容
              </template>
              {{ formValue.content }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                通知号码
              </template>
              <span v-html="formValue.mobiles"></span>
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                关联用户ID
              </template>
              {{ formValue.memberId }}
            </n-descriptions-item>
            <n-descriptions-item label="状态">
              <n-tag :type="dict.getType('sys_normal_disable', formValue.status)" size="small" class="min-left-space">
                {{ dict.getLabel('sys_normal_disable', formValue.status) }}
              </n-tag>
            </n-descriptions-item>
          </n-descriptions>
        </n-spin>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { useMessage } from 'naive-ui';
  import { View } from '@/api/sysNotifyConfig';
  import { State, newState } from './model';
  import { adaModalWidth } from '@/utils/hotgo';
  import { getFileExt } from '@/utils/urlUtils';
  import { useDictStore } from '@/store/modules/dict';

  const message = useMessage();
  const dict = useDictStore();
  const loading = ref(false);
  const showModal = ref(false);
  const formValue = ref(newState(null));
  const dialogWidth = computed(() => {
    return adaModalWidth(580);
  });
  const fileAvatarCSS = computed(() => {
    return {
      '--n-merged-size': `var(--n-avatar-size-override, 80px)`,
      '--n-font-size': `18px`,
    };
  });

  // 下载
  function download(url: string) {
    window.open(url);
  }

  // 打开模态框
  function openModal(state: State) {
    showModal.value = true;
    loading.value = true;
    View({ id: state.id })
      .then((res) => {
        formValue.value = res;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  defineExpose({
    openModal,
  });
</script>

<style lang="less" scoped></style>