import { h, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { FormSchema } from '@/components/Form';
import { defRangeShortcuts } from '@/utils/dateUtil';
import { renderOptionTag, renderPopoverMemberSumma, MemberSumma } from '@/utils';
import { TreeOption } from '@/api/smsChannel';
import { useDictStore } from '@/store/modules/dict';

const dict = useDictStore();

export class State {
  public pid = 0; // 上级通道
  public id = 0; // ID
  public channelName = ''; // 通道名称
  public protocol = 0; // 通道协议
  public flag = 0; // 运营商标识
  public unitPrice = 0; // 短信单价
  public host = ''; // 接口地址
  public account = ''; // 接口账号
  public passwd = ''; // 接口密码
  public params = ''; // 接口参数
  public extCode = ''; // 扩展码
  public description = ''; // 描述
  public level = 1; // 关系树级别
  public tree = ''; // 关系树
  public sort = 0; // 排序
  public status = 1; // 状态
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 修改时间
  public deletedAt = ''; // 删除时间
  public channelType = ''; // 通道类型

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则
export const rules = {
  channelName: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'string',
    message: '请输入通道名称',
  },
  channelType: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'string',
    message: '请输入通道名称',
  },
  protocol: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: '请输入通道协议',
  },
  flag: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: '请输入运营商标识',
  },
  unitPrice: {
    required: true,
    trigger: ['blur', 'input'],
    type: 'number',
    message: '请输入短信单价',
  },
};

// 表格搜索表单
export const schemas = ref<FormSchema[]>([
  {
    field: 'channelName',
    component: 'NInput',
    label: '通道名称',
    componentProps: {
      placeholder: '请输入通道名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'status',
    component: 'NSelect',
    label: '状态',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择状态',
      options: dict.getOption('sys_normal_disable'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    field: 'createdAt',
    component: 'NDatePicker',
    label: '创建时间',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      shortcuts: defRangeShortcuts(),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
]);

// 表格列
export const columns = [
  {
    title: 'ID',
    key: 'id',
    align: 'left',
    width: -1,
  },
  {
    title: '通道名称',
    key: 'channelName',
    align: 'left',
    width: -1,
  },
  {
    title: '通道协议',
    key: 'protocol',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('sys_channel_protocol', row.protocol);
    },
  },
  {
    title: '运营商标识',
    key: 'flag',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('sys_sms_operators', row.flag);
    },
  },
  {
    title: '短信单价',
    key: 'unitPrice',
    align: 'left',
    width: -1,
  },
  {
    title: '接口地址',
    key: 'host',
    align: 'left',
    width: -1,
  },
  {
    title: '接口账号',
    key: 'account',
    align: 'left',
    width: -1,
  },
  {
    title: '接口密码',
    key: 'passwd',
    align: 'left',
    width: -1,
  },
  {
    title: '接口参数',
    key: 'params',
    align: 'left',
    width: -1,
  },
  {
    title: '扩展码',
    key: 'extCode',
    align: 'left',
    width: -1,
  },
  {
    title: '描述',
    key: 'description',
    align: 'left',
    width: -1,
  },
  {
    title: '状态',
    key: 'status',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderOptionTag('sys_normal_disable', row.status);
    },
  },
  {
    title: '创建者',
    key: 'createdBy',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderPopoverMemberSumma(row.createdBySumma);
    },
  },
  {
    title: '更新者',
    key: 'updatedBy',
    align: 'left',
    width: -1,
    render(row: State) {
      return renderPopoverMemberSumma(row.updatedBySumma);
    },
  },
  {
    title: '创建时间',
    key: 'createdAt',
    align: 'left',
    width: -1,
  },
  {
    title: '修改时间',
    key: 'updatedAt',
    align: 'left',
    width: -1,
  },
];

// 加载字典数据选项
export function loadOptions() {
  dict.loadOptions(['sys_normal_disable', 'sys_channel_protocol', 'sys_sms_operators', 'sms_channel_type']);
}

// 关系树选项
export const treeOption = ref([]);

// 加载关系树选项
export function loadTreeOption() {
  TreeOption().then((res) => {
    treeOption.value = res;
  });
}