<template>
  <div>
    <n-modal
      v-model:show="showModal"
      :mask-closable="false"
      :show-icon="false"
      preset="dialog"
      transform-origin="center"
      :title="formValue.id > 0 ? '编辑企业列表 #' + formValue.id : '添加企业列表'"
      :style="{
        width: dialogWidth,
      }"
    >
      <n-scrollbar style="max-height: 87vh" class="pr-5">
        <n-spin :show="loading" description="请稍候...">
          <n-form
            ref="formRef"
            :model="formValue"
            :rules="rules"
            :label-placement="settingStore.isMobile ? 'top' : 'left'"
            :label-width="100"
            class="py-4"
          >
            <n-grid cols="1 s:1 m:1 l:1 xl:1 2xl:1" responsive="screen">
              <n-gi span="1">
                <n-form-item label="上级企业" path="pid" v-if="formValue.pid > 0">
                  <n-tree-select
                    :options="treeOption"
                    v-model:value="formValue.pid"
                    key-field="id"
                    label-field="companyName"
                    clearable
                    filterable
                    default-expand-all
                    show-path
                    disabled
                  />
                </n-form-item>
              </n-gi>
              <!-- <n-gi span="1">
                <n-form-item label="部门ID" path="deptId">
                  <n-input-number placeholder="请输入部门ID" v-model:value="formValue.deptId" />
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="用户ID" path="memberId">
                  <n-input-number placeholder="请输入用户ID" v-model:value="formValue.memberId" />
                </n-form-item>
              </n-gi> -->
              <n-gi span="1">
                <n-form-item label="企业名称" path="companyName">
                  <n-input placeholder="请输入企业名称" v-model:value="formValue.companyName" />
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="企业账号" path="userName">
                  <n-input placeholder="请输入企业账号" v-model:value="formValue.userName" />
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="登录密码" path="password" v-if="formValue.id === 0">
                  <n-input placeholder="请输入登录账号" v-model:value="formValue.password" />
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="企业负责人" path="leader">
                  <n-input placeholder="请输入负责人" v-model:value="formValue.leader" />
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="联系电话" path="phone">
                  <n-input placeholder="请输入联系电话" v-model:value="formValue.phone" />
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="企业邮箱" path="email">
                  <n-input placeholder="请输入邮箱" v-model:value="formValue.email" />
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="企业地址" path="address">
                  <n-input placeholder="请输入地址" v-model:value="formValue.address" />
                </n-form-item>
              </n-gi>
              <!-- <n-gi span="1">
                <n-form-item label="企业附件" path="attachfiles">
                  <n-input placeholder="请输入附件列表" v-model:value="formValue.attachfiles" />
                </n-form-item>
              </n-gi> -->
              <n-gi span="1">
                <n-form-item label="短信通道" path="channelId" v-if="formValue.pid > 0">
                  <n-tree-select
                    placeholder="请选择短信通道"
                    v-model:value="formValue.channelId"
                    :options="channelTreeOption"
                    key-field="id"
                    label-field="channelName"
                    clearable
                    filterable
                    default-expand-all
                    show-path
                  />
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="短信类型" path="smsType" v-if="formValue.pid > 0">
                  <n-radio-group v-model:value="formValue.smsType" name="smsType">
                    <n-radio-button
                      v-for="smsType in dict.getOptionUnRef('sms_type')"
                        :key="smsType.value"
                        :value="smsType.value"
                        :label="smsType.label"
                    />
                  </n-radio-group>
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="群发鉴权模式" path="groupSendAuditMode" v-if="formValue.pid > 0">
                  <n-radio-group v-model:value="formValue.groupSendAuditMode" name="groupSendAuditMode">
                    <n-radio-button
                      v-for="groupSendAuditMode in dict.getOptionUnRef('auth_type')"
                      :key="groupSendAuditMode.value"
                      :value="groupSendAuditMode.value"
                      :label="groupSendAuditMode.label"
                    />
                  </n-radio-group>
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="模板鉴权模式" path="templateAuditMode" v-if="formValue.pid > 0">
                  <n-radio-group v-model:value="formValue.templateAuditMode" name="templateAuditMode">
                    <n-radio-button
                      v-for="templateAuditMode in dict.getOptionUnRef('auth_type')"
                      :key="templateAuditMode.value"
                      :value="templateAuditMode.value"
                      :label="templateAuditMode.label"
                    />
                  </n-radio-group>
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="重发通道" path="resendChannelConfigs" v-if="hasResendChannelPermission">
                  <div>
                    <!-- 添加通道选择器 -->
                    <n-space vertical>
                      <n-select
                        placeholder="请选择要添加的重发通道"
                        :options="availableChannelOptions"
                        filterable
                        clearable
                        @update:value="addResendChannel"
                        :value="null"
                      />
                      
                      <!-- 已配置的重发通道列表（可拖拽排序） -->
                      <div v-if="formValue.resendChannelConfigs.length > 0">
                        <n-text strong style="font-size: 14px; margin-bottom: 8px; display: block;">
                          已配置通道（拖拽调整优先级）
                        </n-text>
                        <n-space vertical size="small">
                          <div
                            v-for="(config, index) in formValue.resendChannelConfigs"
                            :key="config.channelId"
                            :draggable="true"
                            @dragstart="onDragStart(index)"
                            @dragover.prevent
                            @drop="onDrop(index)"
                            style="
                              padding: 8px 12px;
                              border: 1px solid #e0e0e6;
                              border-radius: 6px;
                              background: #fafafc;
                              cursor: move;
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            "
                          >
                            <n-space align="center">
                              <n-icon size="16" style="color: #666;">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                  <path d="M7 19v-2h2v2H7zm4 0v-2h2v2h-2zm4 0v-2h2v2h-2zM7 15v-2h2v2H7zm4 0v-2h2v2h-2zm4 0v-2h2v2h-2zM7 11V9h2v2H7zm4 0V9h2v2h-2zm4 0V9h2v2h-2z"/>
                                </svg>
                              </n-icon>
                              <n-tag type="info" size="small">
                                优先级 {{ index + 1 }}
                              </n-tag>
                              <span>{{ getChannelName(config.channelId) }}</span>
                            </n-space>
                            <n-button 
                              size="tiny" 
                              type="error" 
                              secondary 
                              @click="removeResendChannel(index)"
                            >
                              删除
                            </n-button>
                          </div>
                        </n-space>
                      </div>
                    </n-space>
                    
                    <n-text depth="3" style="font-size: 12px; margin-top: 8px; display: block;">
                      配置重发通道后，当主通道发送失败时将按照优先级顺序自动使用重发通道重试
                    </n-text>
                  </div>
                </n-form-item>
              </n-gi>
              <n-gi span="1">
                <n-form-item label="企业附件" path="attachfiles" v-if="formValue.pid > 0">
                  <n-upload
                      directory-dnd
                      :custom-request="handleUpload"
                      :on-remove="handleRemove"
                      name="file"
                      :disabled="uploadStatus != 0 && uploadStatus != 3"
                      :file-list="fileList"
                      @update:file-list="handleUpdateFileList"
                    >
                      <n-upload-dragger>
                        <div style="margin-bottom: 12px">
                          <n-icon size="48" :depth="3">
                            <FileAddOutlined />
                          </n-icon>
                        </div>
                        <template v-if="uploadStatus == 0 || uploadStatus == 3">
                          <n-text style="font-size: 16px">点击或者拖动企业附件到该区域来上传</n-text>
                          <n-p depth="3" style="margin: 8px 0 0 0">支持大文件分片上传，支持断点续传</n-p>
                        </template>
                        <template v-else-if="uploadStatus == 1">
                          <span style="font-weight: 600">解析中，请稍候...</span>
                        </template>
                        <template v-else-if="uploadStatus == 2">
                          <span style="font-weight: 600">正在上传({{ progress }}%)...</span>
                          <n-p depth="3" style="margin: 8px 0 0 0">文件大小：{{ sizeFormat }}</n-p>
                        </template>
                      </n-upload-dragger>
                    </n-upload>
                </n-form-item>
              </n-gi>
            </n-grid>
          </n-form>
        </n-spin>
      </n-scrollbar>
      <template #action>
        <n-space>
          <n-button @click="closeForm">
            取消
          </n-button>
          <n-button type="info" :loading="formBtnLoading" @click="confirmForm">
            确定
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useDictStore } from '@/store/modules/dict';
  import { Edit, View, MaxSort } from '@/api/adminCompany';
  import { State, newState, treeOption, loadTreeOption, rules, channelTreeOption, loadChannelTreeOption } from './model';
  import { useProjectSettingStore } from '@/store/modules/projectSetting';
  import { useUserStore } from '@/store/modules/user';
  import { UploadCustomRequestOptions, useDialog, useMessage, UploadFileInfo } from 'naive-ui';
  import { adaModalWidth } from '@/utils/hotgo';
  import { UploadFileParams } from '@/utils/http/axios/types';
  import SparkMD5 from 'spark-md5';
  import { CheckMultipart, UploadPart } from '@/api/base';
  import { Attachment } from '@/components/FileChooser/src/model';
  import { FileAddOutlined } from '@vicons/antd';

  const emit = defineEmits(['reloadTable']);
  const message = useMessage();
  const settingStore = useProjectSettingStore();
  const dict = useDictStore();
  const userStore = useUserStore();
  const loading = ref(false);
  const showModal = ref(false);
  const formValue = ref<State>(newState(null));
  const formRef = ref<any>({});
  const formBtnLoading = ref(false);
  const dialogWidth = computed(() => {
    return adaModalWidth(840);
  });

  // 检查是否有重发渠道配置权限（超级管理员和管理员）
  const hasResendChannelPermission = computed(() => {
    const userInfo = userStore.getUserInfo;
    if (!userInfo) return false;
    // 根据角色ID判断：1=超级管理员, 2=管理员
    return userInfo.roleId === 1 || userInfo.roleId === 2 || userInfo.roleId === 224;
  });

  // 添加文件列表管理
  const fileList = ref<UploadFileInfo[]>([]);
  // 用于表单提交的文件列表
  const submitFileList = ref<UploadFileInfo[]>([]);

  // 重发通道相关
  let dragIndex = -1; // 拖拽起始索引

  // 可用通道选项（排除已配置的）
  const availableChannelOptions = computed(() => {
    const configuredIds = formValue.value.resendChannelConfigs.map(config => config.channelId);
    return channelTreeOption.value
      .filter(option => !configuredIds.includes(option.id))
      .map(option => ({
        label: option.channelName,
        value: option.id
      }));
  });

  // 添加重发通道
  function addResendChannel(channelId: number | null) {
    if (!channelId) return;
    
    // 检查是否已存在
    const exists = formValue.value.resendChannelConfigs.some(config => config.channelId === channelId);
    if (exists) {
      message.warning('该通道已配置');
      return;
    }

    // 添加到配置列表，优先级为当前最大值+1
    const maxPriority = formValue.value.resendChannelConfigs.length > 0 
      ? Math.max(...formValue.value.resendChannelConfigs.map(c => c.priority))
      : 0;
    
    formValue.value.resendChannelConfigs.push({
      channelId,
      priority: maxPriority + 1
    });

    // 更新兼容字段
    updateResendChannels();
  }

  // 删除重发通道
  function removeResendChannel(index: number) {
    formValue.value.resendChannelConfigs.splice(index, 1);
    // 重新分配优先级
    formValue.value.resendChannelConfigs.forEach((config, idx) => {
      config.priority = idx + 1;
    });
    // 更新兼容字段
    updateResendChannels();
  }

  // 获取通道名称
  function getChannelName(channelId: number): string {
    const channel = channelTreeOption.value.find(option => option.id === channelId);
    return channel ? channel.channelName : `通道ID: ${channelId}`;
  }

  // 拖拽开始
  function onDragStart(index: number) {
    dragIndex = index;
  }

  // 拖拽放置
  function onDrop(targetIndex: number) {
    if (dragIndex === -1 || dragIndex === targetIndex) return;

    const configs = [...formValue.value.resendChannelConfigs];
    const draggedItem = configs[dragIndex];
    
    // 移除拖拽项
    configs.splice(dragIndex, 1);
    // 插入到目标位置
    configs.splice(targetIndex, 0, draggedItem);
    
    // 重新分配优先级
    configs.forEach((config, index) => {
      config.priority = index + 1;
    });
    
    formValue.value.resendChannelConfigs = configs;
    // 更新兼容字段
    updateResendChannels();
    
    dragIndex = -1;
  }

  // 更新兼容的resendChannels字段
  function updateResendChannels() {
    formValue.value.resendChannels = formValue.value.resendChannelConfigs.map(config => config.channelId);
  }

  // 提交表单
  function confirmForm(e) {
    e.preventDefault();
    formRef.value.validate((errors) => {
      if (!errors) {
        formBtnLoading.value = true;
        // 转换文件列表为对象数组，包含name和url
        formValue.value.attachfiles = JSON.stringify(submitFileList.value.map(file => ({
          name: file.name,
          url: file.url
        })));
        Edit(formValue.value)
          .then((_res) => {
            message.success('操作成功');
            closeForm();
            emit('reloadTable');
          })
          .finally(() => {
            formBtnLoading.value = false;
          });
      } else {
        message.error('请填写完整信息');
      }
    });
  }

  // 关闭表单
  function closeForm() {
    showModal.value = false;
    loading.value = false;
    // 清空文件列表
    fileList.value = [];
    submitFileList.value = [];
  }

  // 打开模态框
  function openModal(state: State) {
    showModal.value = true;
    
    // 加载关系树选项
    loadTreeOption();
    loadChannelTreeOption();

    // 新增
    if (!state || state.id < 1) {
      formValue.value = newState(state);
      fileList.value = []; // 只在新增时清空文件列表
      submitFileList.value = []; // 清空提交列表
      loading.value = true;
      MaxSort()
        .then((res) => {
          formValue.value.sort = res.sort + 1;
        })
        .finally(() => {
          loading.value = false;
        });
      return;
    }

    // 编辑
    loading.value = true;
    View({ id: state.id })
      .then((res) => {
        formValue.value = res;
        
        // 确保重发通道配置字段的初始化
        if (!formValue.value.resendChannelConfigs) {
          formValue.value.resendChannelConfigs = [];
        }
        
        // 如果有重发通道信息，转换为配置格式
        if (formValue.value.resendChannelInfos && formValue.value.resendChannelInfos.length > 0) {
          formValue.value.resendChannelConfigs = formValue.value.resendChannelInfos.map(info => ({
            channelId: info.channelId,
            priority: info.priority
          }));
        }
        
        // 确保重发通道字段的初始化（兼容性）
        if (!formValue.value.resendChannels) {
          formValue.value.resendChannels = [];
        }
        
        // console.log('View返回的原始数据:', res);
        
        // 处理附件回显
        if (res.attachfiles) {
          try {
            let attachList = Array.isArray(res.attachfiles) 
              ? res.attachfiles 
              : typeof res.attachfiles === 'string' 
                ? res.attachfiles.startsWith('[') 
                  ? JSON.parse(res.attachfiles) 
                  : [{ name: '附件', url: res.attachfiles }]
                : [];

            console.log('解析后的attachList:', attachList);

            // 转换为UploadFileInfo格式
            const files = attachList
              .filter(item => item.url && item.url.trim())
              .map((item, index) => ({
                id: `file_${index + 1}`,
                name: item.name || item.url.substring(item.url.lastIndexOf('/') + 1),
                status: 'finished' as const,
                url: item.url.trim()
              }));
            
            // 更新两个列表
            fileList.value = files;
            submitFileList.value = files;
            
            // console.log('最终的fileList:', fileList.value);
          } catch (e) {
            // console.error('解析附件列表失败:', e);
            // console.error('错误详情:', e);
            fileList.value = [];
            submitFileList.value = [];
          }
        } else {
          // console.log('没有附件数据');
          fileList.value = [];
          submitFileList.value = [];
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  // 大文件分片上传
  const dialog = useDialog();
  const chunkSize = 2 * 1024 * 1024; // 每个分片大小限制，默认2M
  const uploadStatus = ref(0); // 上传状态 0等待上传 1解析中 2上传中 3已取消
  const progress = ref(0);
  const sizeFormat = ref('0B');

  // 取消上传
  function handleRemove(data: { file: UploadFileInfo }) {
    console.log('handleRemove', data)
    if (uploadStatus.value == 1 || uploadStatus.value == 2) {
      uploadStatus.value = 3;
      dialog.info({
        title: '提示',
        content: '已取消大文件上传，已上传的文件不会自动删除，重新操作可进行断点续传',
        positiveText: '确定',
      });
    }
    // 从文件列表中移除指定文件
    fileList.value = fileList.value.filter(file => file.id !== data.file.id);
    submitFileList.value = submitFileList.value.filter(file => file.id !== data.file.id);
  }

  // 开始上传
  function handleUpload(options: UploadCustomRequestOptions) {
    uploadStatus.value = 1;

    // 初始化上传进度
    updateProgress(options, 0);

    const file = options.file.file as File;
    const fileReader = new FileReader();
    fileReader.readAsArrayBuffer(file);
    fileReader.onload = async (e) => {
      const spark = new SparkMD5.ArrayBuffer();
      spark.append(e.target.result);
      let md5 = spark.end();
      let start = 0;
      let end = 0;
      let index = 0;
      let shards: any[] = [];
      while (end < file.size) {
        start = index * chunkSize;
        end = (index + 1) * chunkSize;

        const params: UploadFileParams = {
          uploadType: 'doc',
          md5: md5,
          index: index + 1,
          fileName: file.name,
          file: file.slice(start, end),
        };

        const shard = { index: index + 1, params: params };
        shards.push(shard);
        index++;
      }

      uploadStatus.value = 2;

      const params = {
        // uploadType: 'doc',
        fileName: file.name,
        size: file.size,
        md5: md5,
        shardCount: shards.length,
      };

      CheckMultipart(params)
        .then(async (res) => {
          // 已存在
          if (!res.waitUploadIndex || res.waitUploadIndex.length == 0) {
            onFinish(options, res.attachment);
            return;
          }

          // 断点续传，过滤掉已上传成功的分片文件
          shards = shards.filter((shard) => res.waitUploadIndex.includes(shard.index));
          if (shards.length == 0) {
            onFinish(options, res.attachment);
            return;
          }

          // 导入断点续传进度
          updateProgress(options, res.progress);
          sizeFormat.value = res.sizeFormat;

          for (const item of shards) {
            if (uploadStatus.value == 3) {
              break;
            }
            item.params.uploadId = res.uploadId;
            await handleUploadPart(options, item);
          }
        })
        .catch(() => {
          uploadStatus.value = 0;
          options.onError();
        });
    };
  }

  // 上传分片文件
  async function handleUploadPart(options: UploadCustomRequestOptions, item) {
    const res = await UploadPart(item.params);
    updateProgress(options, res.progress);
    if (res.finish) {
      onFinish(options, res.attachment);
    }
  }

  // 更新上传进度
  function updateProgress(options: UploadCustomRequestOptions, value: number) {
    options.onProgress({ percent: value });
    progress.value = value;
  }

  // 上传成功后的回调
  function onFinish(options: UploadCustomRequestOptions, result: Attachment) {
    console.log('onFinish', result);
    options.onFinish();
    message.success('上传成功');
    uploadStatus.value = 0;
    
    // 添加到提交列表
    const newFile = {
      id: `file_${result.id}`,
      name: result.name || result.fileUrl.substring(result.fileUrl.lastIndexOf('/') + 1),
      status: 'finished' as const,
      url: result.fileUrl
    };
    submitFileList.value = [...submitFileList.value, newFile];
  }

  function handleUpdateFileList(files: UploadFileInfo[]) {
    // console.log('文件列表更新，当前列表长度:', files.length);
    // console.log('当前文件列表:', files);
    
    // 如果是删除操作，直接通过handleRemove处理
    if (files.length < fileList.value.length) {
      return;
    }
    
    // 必须更新文件列表，否则上传组件不会触发上传
    fileList.value = files;
  }

  function handleBeforeUpload({ file }: { file: UploadFileInfo }) {
    console.log('before upload:', file);
    // 如果文件已经有URL，说明是已上传的文件，不需要重新上传
    if (file.url) {
      return false;
    }
    // 新文件需要上传
    uploadStatus.value = 0; // 重置上传状态
    return true;
  }

  defineExpose({
    openModal,
  });
</script>

<style lang="less"></style>