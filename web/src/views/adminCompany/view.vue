<template>
  <div>
    <n-drawer v-model:show="showModal" :width="dialogWidth">
      <n-drawer-content title="企业详情" closable>
        <n-spin :show="loading" description="请稍候...">
          <n-descriptions label-placement="left" class="py-2" :column="1">
            <!-- <n-descriptions-item>
              <template #label>
                父ID
              </template>
              {{ formValue.pid }}
            </n-descriptions-item> -->
            <!-- <n-descriptions-item>
              <template #label>
                部门ID
              </template>
              {{ formValue.deptId }}
            </n-descriptions-item> -->
            <n-descriptions-item>
              <template #label>
                企业ID
              </template>
              {{ formValue.memberId }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                企业名称
              </template>
              {{ formValue.companyName }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                企业账号
              </template>
              {{ formValue.userName }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                企业负责人
              </template>
              {{ formValue.leader }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                联系电话
              </template>
              {{ formValue.phone }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                企业邮箱
              </template>
              {{ formValue.email }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                企业地址
              </template>
              {{ formValue.address }}
            </n-descriptions-item>
            <!-- <n-descriptions-item>
              <template #label>
                附件列表
              </template>
              {{ formValue.attachfiles }}
            </n-descriptions-item> -->
            <n-descriptions-item>
              <template #label>
                短信通道
              </template>
              {{ formValue.smsChannelName }}
            </n-descriptions-item>
            <n-descriptions-item label="短信类型">
              <!-- <template #label>
                短信类型
              </template>
              {{ formValue.smsType }} -->
              <n-tag :type="dict.getType('sms_type', formValue.smsType)" size="small" class="min-left-space">
                {{ dict.getLabel('sms_type', formValue.smsType) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="群发鉴权模式">
              <n-tag :type="dict.getType('auth_type', formValue.groupSendAuditMode)" size="small" class="min-left-space">
                {{ dict.getLabel('auth_type', formValue.groupSendAuditMode) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="模板鉴权模式">
              <n-tag :type="dict.getType('auth_type', formValue.templateAuditMode)" size="small" class="min-left-space">
                {{ dict.getLabel('auth_type', formValue.templateAuditMode) }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item v-if="hasResendChannelPermission && formValue.resendChannelInfos && formValue.resendChannelInfos.length > 0">
              <template #label>
                重发通道
              </template>
              <n-space vertical size="small">
                <n-space>
                  <n-tag
                    v-for="channel in formValue.resendChannelInfos"
                    :key="channel.channelId"
                    type="info"
                    size="small"
                  >
                    优先级{{ channel.priority }} - {{ channel.channelName }}
                  </n-tag>
                </n-space>
              </n-space>
              <n-text depth="3" style="font-size: 12px; margin-top: 4px; display: block;">
                当主通道发送失败时将按照优先级顺序自动使用这些通道重试
              </n-text>
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                企业密钥
              </template>
              {{ formValue.secretKey }}
            </n-descriptions-item>
            <n-descriptions-item>
              <template #label>
                企业附件
              </template>
              <div v-if="fileList.length > 0">
                <n-space vertical>
                  <n-space v-for="file in fileList" :key="file.id">
                    <n-button text type="primary" @click="download(file.url)">
                      {{ file.name }}
                    </n-button>
                  </n-space>
                </n-space>
              </div>
              <span v-else>无附件</span>
            </n-descriptions-item>
          </n-descriptions>
        </n-spin>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { useMessage } from 'naive-ui';
  import { View } from '@/api/adminCompany';
  import { State, newState } from './model';
  import { adaModalWidth } from '@/utils/hotgo';
  import { getFileExt } from '@/utils/urlUtils';
  import { useDictStore } from '@/store/modules/dict';
  import { useUserStore } from '@/store/modules/user';

  const message = useMessage();
  const dict = useDictStore();
  const userStore = useUserStore();
  const loading = ref(false);
  const showModal = ref(false);
  const formValue = ref(newState(null));
  const fileList = ref<{ id: string; name: string; status: string; url: string }[]>([]);
  const dialogWidth = computed(() => {
    return adaModalWidth(580);
  });
  const fileAvatarCSS = computed(() => {
    return {
      '--n-merged-size': `var(--n-avatar-size-override, 80px)`,
      '--n-font-size': `18px`,
    };
  });

  // 检查是否有重发渠道配置权限（超级管理员和管理员）
  const hasResendChannelPermission = computed(() => {
    const userInfo = userStore.getUserInfo;
    if (!userInfo) return false;
    // 根据角色ID判断：1=超级管理员, 2=管理员
    return userInfo.roleId === 1 || userInfo.roleId === 2;
  });

  // 下载
  function download(url: string) {
    window.open(url);
  }

  // 打开模态框
  function openModal(state: State) {
    showModal.value = true;
    loading.value = true;
    View({ id: state.id })
      .then((res) => {
        formValue.value = res;
        console.log('View返回的原始数据:', res);
        
        // 处理附件回显
        if (res.attachfiles) {
          try {
            let attachList = Array.isArray(res.attachfiles) 
              ? res.attachfiles 
              : typeof res.attachfiles === 'string' 
                ? res.attachfiles.startsWith('[') 
                  ? JSON.parse(res.attachfiles) 
                  : [{ name: '附件', url: res.attachfiles }]
                : [];

            console.log('解析后的attachList:', attachList);

            // 转换为文件列表格式
            fileList.value = attachList
              .filter(item => item.url && item.url.trim())
              .map((item, index) => ({
                id: `file_${index + 1}`,
                name: item.name || item.url.substring(item.url.lastIndexOf('/') + 1),
                status: 'finished',
                url: item.url.trim()
              }));
            
            console.log('最终的fileList:', fileList.value);
          } catch (e) {
            console.error('解析附件列表失败:', e);
            console.error('错误详情:', e);
            fileList.value = [];
          }
        } else {
          console.log('没有附件数据');
          fileList.value = [];
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  defineExpose({
    openModal,
  });
</script>

<style lang="less" scoped></style>